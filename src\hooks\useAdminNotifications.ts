import { useState, useEffect, useCallback } from 'react';
import { 
  AdminNotification, 
  AdminNotificationFilters,
  AdminNotificationCounts
} from '../types/admin-notifications';
import { AdminNotificationService } from '../services/adminNotificationService';

interface UseAdminNotificationsOptions {
  limit?: number;
  filters?: AdminNotificationFilters;
  autoMarkAsRead?: boolean;
}

export const useAdminNotifications = (options: UseAdminNotificationsOptions = {}) => {
  const { limit = 30, filters = {}, autoMarkAsRead = false } = options;
  
  const [notifications, setNotifications] = useState<AdminNotification[]>([]);
  const [counts, setCounts] = useState<AdminNotificationCounts>({
    total: 0,
    unread: 0,
    byType: {} as any
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Subscribe to notifications
  useEffect(() => {
    setIsLoading(true);
    setError(null);

    const unsubscribe = AdminNotificationService.subscribeToNotifications(
      (newNotifications) => {
        setNotifications(newNotifications);
        setIsLoading(false);
        
        // Auto-mark as read if enabled
        if (autoMarkAsRead && newNotifications.length > 0) {
          const unreadIds = newNotifications
            .filter(n => !n.read)
            .map(n => n.id);
          
          if (unreadIds.length > 0) {
            AdminNotificationService.markMultipleAsRead(unreadIds)
              .catch(console.error);
          }
        }
      },
      filters,
      limit
    );

    return unsubscribe;
  }, [limit, filters, autoMarkAsRead]);

  // Load counts
  useEffect(() => {
    const loadCounts = async () => {
      try {
        const newCounts = await AdminNotificationService.getNotificationCounts();
        setCounts(newCounts);
      } catch (err) {
        console.error('Error loading notification counts:', err);
        setError('Failed to load notification counts');
      }
    };

    loadCounts();
    
    // Refresh counts every 30 seconds
    const interval = setInterval(loadCounts, 30000);
    return () => clearInterval(interval);
  }, []);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await AdminNotificationService.markAsRead(notificationId);
      
      // Update local state
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId ? { ...n, read: true } : n
        )
      );
      
      // Update counts
      setCounts(prev => ({
        ...prev,
        unread: Math.max(0, prev.unread - 1)
      }));
    } catch (err) {
      console.error('Error marking notification as read:', err);
      setError('Failed to mark notification as read');
    }
  }, []);

  // Mark multiple notifications as read
  const markMultipleAsRead = useCallback(async (notificationIds: string[]) => {
    try {
      await AdminNotificationService.markMultipleAsRead(notificationIds);
      
      // Update local state
      setNotifications(prev => 
        prev.map(n => 
          notificationIds.includes(n.id) ? { ...n, read: true } : n
        )
      );
      
      // Update counts
      const unreadCount = notificationIds.filter(id => 
        notifications.find(n => n.id === id && !n.read)
      ).length;
      
      setCounts(prev => ({
        ...prev,
        unread: Math.max(0, prev.unread - unreadCount)
      }));
    } catch (err) {
      console.error('Error marking multiple notifications as read:', err);
      setError('Failed to mark notifications as read');
    }
  }, [notifications]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      await AdminNotificationService.markAllAsRead();
      
      // Update local state
      setNotifications(prev => 
        prev.map(n => ({ ...n, read: true }))
      );
      
      // Update counts
      setCounts(prev => ({
        ...prev,
        unread: 0
      }));
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      setError('Failed to mark all notifications as read');
    }
  }, []);

  // Get unread notifications
  const unreadNotifications = notifications.filter(n => !n.read);

  // Get notifications by type
  const getNotificationsByType = useCallback((type: string) => {
    return notifications.filter(n => n.type === type);
  }, [notifications]);

  // Get recent notifications (last 24 hours)
  const recentNotifications = notifications.filter(n => {
    const notificationDate = n.createdAt.toDate();
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    return notificationDate > oneDayAgo;
  });

  // Generate action URL for notification
  const getActionUrl = useCallback((notification: AdminNotification) => {
    return AdminNotificationService.generateActionUrl(notification);
  }, []);

  return {
    notifications,
    unreadNotifications,
    recentNotifications,
    counts,
    isLoading,
    error,
    markAsRead,
    markMultipleAsRead,
    markAllAsRead,
    getNotificationsByType,
    getActionUrl,
    // Computed values
    hasUnread: counts.unread > 0,
    unreadCount: counts.unread,
    totalCount: counts.total
  };
};
