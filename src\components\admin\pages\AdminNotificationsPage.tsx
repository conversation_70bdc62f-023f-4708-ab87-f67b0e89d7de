import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { 
  <PERSON>, 
  Check, 
  CheckCheck, 
  Filter, 
  Search, 
  Calendar,
  ExternalLink,
  Clock,
  AlertCircle
} from 'lucide-react';
import { useAdminNotifications } from '../../../hooks/useAdminNotifications';
import { AdminNotification, AdminNotificationType, NOTIFICATION_CONFIG } from '../../../types/admin-notifications';
import { formatDistanceToNow, format } from 'date-fns';

const AdminNotificationsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<AdminNotificationType | 'all'>('all');
  const [selectedRead, setSelectedRead] = useState<'all' | 'read' | 'unread'>('all');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);

  const filters = useMemo(() => ({
    ...(selectedType !== 'all' && { type: selectedType }),
    ...(selectedRead !== 'all' && { read: selectedRead === 'read' })
  }), [selectedType, selectedRead]);

  const {
    notifications,
    counts,
    isLoading,
    markAsRead,
    markMultipleAsRead,
    markAllAsRead,
    getActionUrl
  } = useAdminNotifications({ limit: 100, filters });

  // Filter notifications by search term
  const filteredNotifications = useMemo(() => {
    if (!searchTerm) return notifications;
    
    const term = searchTerm.toLowerCase();
    return notifications.filter(notification => 
      notification.title.toLowerCase().includes(term) ||
      notification.message.toLowerCase().includes(term) ||
      notification.username?.toLowerCase().includes(term) ||
      notification.type.toLowerCase().includes(term)
    );
  }, [notifications, searchTerm]);

  const handleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n.id));
    }
  };

  const handleSelectNotification = (notificationId: string) => {
    setSelectedNotifications(prev => 
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    );
  };

  const handleMarkSelectedAsRead = async () => {
    if (selectedNotifications.length > 0) {
      await markMultipleAsRead(selectedNotifications);
      setSelectedNotifications([]);
    }
  };

  const handleNotificationClick = async (notification: AdminNotification) => {
    if (!notification.read) {
      await markAsRead(notification.id);
    }
  };

  const formatRelativeTime = (timestamp: any) => {
    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch {
      return 'Unknown time';
    }
  };

  const formatFullTime = (timestamp: any) => {
    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return format(date, 'PPpp');
    } catch {
      return 'Unknown time';
    }
  };

  const getNotificationIcon = (notification: AdminNotification) => {
    if (notification.icon && notification.icon.length === 1) {
      return <span className="text-lg">{notification.icon}</span>;
    }
    return <Bell className="h-4 w-4" />;
  };

  const getNotificationColor = (notification: AdminNotification) => {
    const config = NOTIFICATION_CONFIG[notification.type];
    switch (config?.color) {
      case 'red':
        return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
      case 'green':
        return 'border-l-green-500 bg-green-50 dark:bg-green-900/20';
      case 'blue':
        return 'border-l-blue-500 bg-blue-50 dark:bg-blue-900/20';
      case 'yellow':
        return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'purple':
        return 'border-l-purple-500 bg-purple-50 dark:bg-purple-900/20';
      default:
        return 'border-l-gray-300 bg-gray-50 dark:bg-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Admin Notifications
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor all platform activity and system events
          </p>
        </div>
        
        {/* Stats */}
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {counts.total}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Total</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">
              {counts.unread}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Unread</div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search notifications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Type Filter */}
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value as AdminNotificationType | 'all')}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Types</option>
            {Object.keys(NOTIFICATION_CONFIG).map((type) => (
              <option key={type} value={type}>
                {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </option>
            ))}
          </select>

          {/* Read Status Filter */}
          <select
            value={selectedRead}
            onChange={(e) => setSelectedRead(e.target.value as 'all' | 'read' | 'unread')}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="unread">Unread</option>
            <option value="read">Read</option>
          </select>
        </div>

        {/* Bulk Actions */}
        {filteredNotifications.length > 0 && (
          <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedNotifications.length === filteredNotifications.length}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Select all ({filteredNotifications.length})
                </span>
              </label>
              
              {selectedNotifications.length > 0 && (
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {selectedNotifications.length} selected
                </span>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {selectedNotifications.length > 0 && (
                <button
                  onClick={handleMarkSelectedAsRead}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-1"
                >
                  <Check className="h-4 w-4" />
                  <span>Mark as Read</span>
                </button>
              )}
              
              {counts.unread > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="px-3 py-1 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-1"
                >
                  <CheckCheck className="h-4 w-4" />
                  <span>Mark All Read</span>
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Notifications List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        {isLoading ? (
          <div className="p-8 text-center">
            <Clock className="h-8 w-8 mx-auto mb-4 animate-spin text-gray-400" />
            <p className="text-gray-500 dark:text-gray-400">Loading notifications...</p>
          </div>
        ) : filteredNotifications.length === 0 ? (
          <div className="p-8 text-center">
            <Bell className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No notifications found
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm || selectedType !== 'all' || selectedRead !== 'all'
                ? 'Try adjusting your filters'
                : 'Notifications will appear here as platform activity occurs'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 border-l-4 ${getNotificationColor(notification)} ${
                  !notification.read ? 'font-medium' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    checked={selectedNotifications.includes(notification.id)}
                    onChange={() => handleSelectNotification(notification.id)}
                    className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {notification.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {notification.message}
                        </p>
                        
                        {/* Metadata */}
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                          <span>{formatRelativeTime(notification.createdAt)}</span>
                          <span title={formatFullTime(notification.createdAt)}>
                            {format(notification.createdAt.toDate(), 'MMM d, yyyy h:mm a')}
                          </span>
                          {notification.username && (
                            <span>by {notification.username}</span>
                          )}
                          <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                            {notification.type.replace(/_/g, ' ')}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                            title="Mark as read"
                          >
                            <Check className="h-4 w-4" />
                          </button>
                        )}
                        
                        <Link
                          to={getActionUrl(notification)}
                          onClick={() => handleNotificationClick(notification)}
                          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                          title="View details"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Link>
                        
                        {!notification.read && (
                          <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminNotificationsPage;
