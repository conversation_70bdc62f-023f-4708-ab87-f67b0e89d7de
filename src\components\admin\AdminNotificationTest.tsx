import React, { useState } from 'react';
import { Bell, Plus, Trash2, Refresh<PERSON>w } from 'lucide-react';
import { AdminNotificationService } from '../../services/adminNotificationService';
import { AdminNotificationType, NOTIFICATION_CONFIG } from '../../types/admin-notifications';
import { seedAdminNotifications } from '../../utils/seedAdminNotifications';

const AdminNotificationTest: React.FC = () => {
  const [isCreating, setIsCreating] = useState(false);
  const [isSeeding, setIsSeeding] = useState(false);
  const [selectedType, setSelectedType] = useState<AdminNotificationType>('user_signup');
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [userId, setUserId] = useState('');
  const [username, setUsername] = useState('');
  const [amount, setAmount] = useState('');

  const handleCreateNotification = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim() || !message.trim()) {
      alert('Please fill in title and message');
      return;
    }

    setIsCreating(true);
    
    try {
      await AdminNotificationService.createNotification(
        selectedType,
        title,
        message,
        {
          userId: userId || undefined,
          username: username || undefined,
          amount: amount ? parseFloat(amount) : undefined,
          metadata: {
            testNotification: true,
            createdAt: new Date().toISOString()
          }
        }
      );

      // Reset form
      setTitle('');
      setMessage('');
      setUserId('');
      setUsername('');
      setAmount('');
      
      alert('Notification created successfully!');
    } catch (error) {
      console.error('Error creating notification:', error);
      alert('Failed to create notification');
    } finally {
      setIsCreating(false);
    }
  };

  const handleSeedNotifications = async () => {
    setIsSeeding(true);
    
    try {
      await seedAdminNotifications();
      alert('Sample notifications created successfully!');
    } catch (error) {
      console.error('Error seeding notifications:', error);
      alert('Failed to create sample notifications');
    } finally {
      setIsSeeding(false);
    }
  };

  const handleTypeChange = (type: AdminNotificationType) => {
    setSelectedType(type);
    
    // Set default values based on type
    const config = NOTIFICATION_CONFIG[type];
    switch (type) {
      case 'user_signup':
        setTitle('New User Signup');
        setMessage('A new user has joined the platform');
        setUsername('Test User');
        break;
      case 'listing_created':
        setTitle('New Listing Created');
        setMessage('A new listing has been created');
        setUsername('Seller Name');
        setAmount('50');
        break;
      case 'payment_completed':
        setTitle('Payment Completed');
        setMessage('Payment has been processed successfully');
        setUsername('Buyer Name');
        setAmount('75');
        break;
      case 'payment_failed':
        setTitle('Payment Failed');
        setMessage('Payment processing failed');
        setUsername('Buyer Name');
        setAmount('100');
        break;
      default:
        setTitle(`Test ${type.replace(/_/g, ' ')}`);
        setMessage(`This is a test notification for ${type.replace(/_/g, ' ')}`);
        setUsername('Test User');
        break;
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-2 mb-6">
          <Bell className="h-6 w-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Admin Notification Test
          </h2>
        </div>

        {/* Quick Actions */}
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Quick Actions
          </h3>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={handleSeedNotifications}
              disabled={isSeeding}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isSeeding ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
              <span>{isSeeding ? 'Creating...' : 'Create Sample Notifications'}</span>
            </button>
          </div>
        </div>

        {/* Create Custom Notification */}
        <form onSubmit={handleCreateNotification} className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Create Custom Notification
          </h3>

          {/* Notification Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notification Type
            </label>
            <select
              value={selectedType}
              onChange={(e) => handleTypeChange(e.target.value as AdminNotificationType)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              {Object.keys(NOTIFICATION_CONFIG).map((type) => (
                <option key={type} value={type}>
                  {NOTIFICATION_CONFIG[type as AdminNotificationType].icon} {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </option>
              ))}
            </select>
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Title
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              placeholder="Notification title"
              required
            />
          </div>

          {/* Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Message
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              placeholder="Notification message"
              required
            />
          </div>

          {/* Optional Fields */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                User ID (optional)
              </label>
              <input
                type="text"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                placeholder="user123"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Username (optional)
              </label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                placeholder="John Doe"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Amount (optional)
              </label>
              <input
                type="number"
                step="0.01"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                placeholder="0.00"
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isCreating}
              className="flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {isCreating ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
              <span>{isCreating ? 'Creating...' : 'Create Notification'}</span>
            </button>
          </div>
        </form>

        {/* Instructions */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            Testing Instructions
          </h4>
          <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li>• Use "Create Sample Notifications" to generate test data</li>
            <li>• Create custom notifications to test specific scenarios</li>
            <li>• Check the bell icon in the header for real-time updates</li>
            <li>• Visit the full notifications page to see all notifications</li>
            <li>• Test marking notifications as read/unread</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AdminNotificationTest;
