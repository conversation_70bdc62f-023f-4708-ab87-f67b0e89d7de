import {
  collection,
  addDoc,
  query,
  orderBy,
  limit,
  where,
  onSnapshot,
  updateDoc,
  doc,
  writeBatch,
  Timestamp,
  serverTimestamp,
  getDocs
} from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { 
  AdminNotification, 
  AdminNotificationType, 
  AdminNotificationFilters,
  AdminNotificationCounts,
  NOTIFICATION_CONFIG
} from '../types/admin-notifications';

const ADMIN_NOTIFICATIONS_COLLECTION = 'admin_notifications';

export class AdminNotificationService {
  
  /**
   * Create a new admin notification
   */
  static async createNotification(
    type: AdminNotificationType,
    title: string,
    message: string,
    options: {
      userId?: string;
      username?: string;
      orderId?: string;
      listingId?: string;
      amount?: number;
      metadata?: Record<string, any>;
      actionUrl?: string;
    } = {}
  ): Promise<string> {
    try {
      const config = NOTIFICATION_CONFIG[type];
      
      const notification = {
        type,
        title,
        message,
        icon: config.icon,
        userId: options.userId,
        username: options.username,
        orderId: options.orderId,
        listingId: options.listingId,
        amount: options.amount,
        metadata: options.metadata || {},
        actionUrl: options.actionUrl,
        read: false,
        createdAt: serverTimestamp()
      };

      const docRef = await addDoc(
        collection(firestore, ADMIN_NOTIFICATIONS_COLLECTION),
        notification
      );

      console.log('Admin notification created:', { id: docRef.id, type, title });
      return docRef.id;
    } catch (error) {
      console.error('Error creating admin notification:', error);
      throw error;
    }
  }

  /**
   * Subscribe to admin notifications with real-time updates
   */
  static subscribeToNotifications(
    callback: (notifications: AdminNotification[]) => void,
    filters: AdminNotificationFilters = {},
    limitCount: number = 50
  ): () => void {
    try {
      let q = query(
        collection(firestore, ADMIN_NOTIFICATIONS_COLLECTION),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      // Apply filters
      if (filters.type) {
        q = query(q, where('type', '==', filters.type));
      }

      if (filters.read !== undefined) {
        q = query(q, where('read', '==', filters.read));
      }

      if (filters.userId) {
        q = query(q, where('userId', '==', filters.userId));
      }

      const unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          const notifications: AdminNotification[] = [];

          snapshot.forEach((doc) => {
            notifications.push({
              id: doc.id,
              ...doc.data()
            } as AdminNotification);
          });

          callback(notifications);
        },
        (error) => {
          console.error('Error listening to admin notifications:', error);
          // Return empty array on permission errors to prevent UI crashes
          if (error.code === 'permission-denied') {
            callback([]);
          }
        }
      );

      return unsubscribe;
    } catch (error) {
      console.error('Error setting up notifications subscription:', error);
      // Return empty array on setup errors
      callback([]);
      return () => {};
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string): Promise<void> {
    try {
      const notificationRef = doc(firestore, ADMIN_NOTIFICATIONS_COLLECTION, notificationId);
      await updateDoc(notificationRef, {
        read: true
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark multiple notifications as read
   */
  static async markMultipleAsRead(notificationIds: string[]): Promise<void> {
    try {
      const batch = writeBatch(firestore);
      
      notificationIds.forEach((id) => {
        const notificationRef = doc(firestore, ADMIN_NOTIFICATIONS_COLLECTION, id);
        batch.update(notificationRef, { read: true });
      });

      await batch.commit();
    } catch (error) {
      console.error('Error marking multiple notifications as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read
   */
  static async markAllAsRead(): Promise<void> {
    try {
      const q = query(
        collection(firestore, ADMIN_NOTIFICATIONS_COLLECTION),
        where('read', '==', false)
      );
      
      const snapshot = await getDocs(q);
      const batch = writeBatch(firestore);
      
      snapshot.forEach((doc) => {
        batch.update(doc.ref, { read: true });
      });

      await batch.commit();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Get notification counts
   */
  static async getNotificationCounts(): Promise<AdminNotificationCounts> {
    try {
      const allQuery = query(collection(firestore, ADMIN_NOTIFICATIONS_COLLECTION));
      const unreadQuery = query(
        collection(firestore, ADMIN_NOTIFICATIONS_COLLECTION),
        where('read', '==', false)
      );

      const [allSnapshot, unreadSnapshot] = await Promise.all([
        getDocs(allQuery),
        getDocs(unreadQuery)
      ]);

      const byType: Record<AdminNotificationType, number> = {} as any;

      // Initialize counts
      Object.keys(NOTIFICATION_CONFIG).forEach((type) => {
        byType[type as AdminNotificationType] = 0;
      });

      // Count by type
      allSnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.type && byType[data.type as AdminNotificationType] !== undefined) {
          byType[data.type as AdminNotificationType]++;
        }
      });

      return {
        total: allSnapshot.size,
        unread: unreadSnapshot.size,
        byType
      };
    } catch (error) {
      console.error('Error getting notification counts:', error);
      // Return empty counts on permission errors
      if (error.code === 'permission-denied') {
        const byType: Record<AdminNotificationType, number> = {} as any;
        Object.keys(NOTIFICATION_CONFIG).forEach((type) => {
          byType[type as AdminNotificationType] = 0;
        });
        return {
          total: 0,
          unread: 0,
          byType
        };
      }
      throw error;
    }
  }

  /**
   * Generate action URL for notification
   */
  static generateActionUrl(notification: AdminNotification): string {
    if (notification.actionUrl) {
      return notification.actionUrl;
    }

    // Generate default URLs based on notification type
    switch (notification.type) {
      case 'user_signup':
        return `/admin/users${notification.userId ? `?search=${notification.userId}` : ''}`;
      case 'listing_created':
      case 'listing_sold':
      case 'listing_updated':
        return `/admin/listings${notification.listingId ? `?search=${notification.listingId}` : ''}`;
      case 'order_created':
      case 'payment_completed':
      case 'payment_failed':
        return `/admin/transactions${notification.orderId ? `?search=${notification.orderId}` : ''}`;
      case 'feedback_submitted':
      case 'user_issue':
        return '/admin/reports';
      case 'dispute_created':
        return '/admin/chat';
      case 'wallet_used':
      case 'wallet_added':
        return '/admin/wallet-reports';
      case 'shipping_label_created':
        return '/admin/shipping';
      default:
        return '/admin/dashboard';
    }
  }
}
