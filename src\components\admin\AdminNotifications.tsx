import React from 'react';
import { Link } from 'react-router-dom';
import { Bell } from 'lucide-react';
import { useAdminNotifications } from '../../hooks/useAdminNotifications';

interface AdminNotificationsProps {
  className?: string;
}

const AdminNotifications: React.FC<AdminNotificationsProps> = ({ className = '' }) => {
  const { unreadCount } = useAdminNotifications({ limit: 1 }); // Only need count

  return (
    <div className={className}>
      {/* Bell Icon Button - Direct Link */}
      <Link
        to="/admin/notifications"
        className="relative p-1 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors block"
      >
        <span className="sr-only">View notifications</span>
        <Bell className="h-6 w-6" />

        {/* Notification Badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-medium text-white ring-2 ring-white dark:ring-gray-800">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </Link>
    </div>
  );
};

export default AdminNotifications;
