import { AdminNotificationService } from '../services/adminNotificationService';
import { AdminNotificationType } from '../types/admin-notifications';

// Sample data for generating test notifications
const sampleUsers = [
  { id: 'user1', name: '<PERSON>', email: '<EMAIL>' },
  { id: 'user2', name: '<PERSON>', email: '<EMAIL>' },
  { id: 'user3', name: '<PERSON>', email: '<EMAIL>' },
  { id: 'user4', name: '<PERSON>', email: '<EMAIL>' },
  { id: 'user5', name: '<PERSON>', email: '<EMAIL>' }
];

const sampleListings = [
  { id: 'listing1', title: 'Calculus Textbook', price: 85, category: 'textbooks' },
  { id: 'listing2', title: 'MacBook Pro 2019', price: 1200, category: 'electronics' },
  { id: 'listing3', title: 'Chemistry Lab Kit', price: 45, category: 'supplies' },
  { id: 'listing4', title: '<PERSON><PERSON>', price: 25, category: 'furniture' },
  { id: 'listing5', title: 'Backpack', price: 35, category: 'accessories' }
];

const sampleOrders = [
  { id: 'order1', amount: 85, buyerId: 'user1', sellerId: 'user2' },
  { id: 'order2', amount: 1200, buyerId: 'user3', sellerId: 'user4' },
  { id: 'order3', amount: 45, buyerId: 'user5', sellerId: 'user1' },
  { id: 'order4', amount: 25, buyerId: 'user2', sellerId: 'user3' },
  { id: 'order5', amount: 35, buyerId: 'user4', sellerId: 'user5' }
];

// Helper function to get random item from array
function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// Helper function to get random amount
function getRandomAmount(min: number = 10, max: number = 500): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Generate sample notifications for each type
export const seedAdminNotifications = async (): Promise<void> => {
  console.log('🌱 Seeding admin notifications...');

  try {
    const notifications: Array<{
      type: AdminNotificationType;
      title: string;
      message: string;
      options: any;
    }> = [];

    // User signups
    for (let i = 0; i < 3; i++) {
      const user = getRandomItem(sampleUsers);
      notifications.push({
        type: 'user_signup',
        title: 'New User Signup',
        message: `${user.name} has joined the platform`,
        options: {
          userId: user.id,
          username: user.name,
          metadata: { email: user.email }
        }
      });
    }

    // Listing activities
    for (let i = 0; i < 4; i++) {
      const listing = getRandomItem(sampleListings);
      const user = getRandomItem(sampleUsers);
      
      notifications.push({
        type: 'listing_created',
        title: 'New Listing Created',
        message: `${user.name} created a new listing: ${listing.title}`,
        options: {
          userId: user.id,
          username: user.name,
          listingId: listing.id,
          amount: listing.price,
          metadata: { category: listing.category },
          actionUrl: `/admin/listings?search=${listing.id}`
        }
      });
    }

    // Listing sold
    for (let i = 0; i < 2; i++) {
      const listing = getRandomItem(sampleListings);
      const user = getRandomItem(sampleUsers);
      
      notifications.push({
        type: 'listing_sold',
        title: 'Listing Sold',
        message: `${user.name} sold: ${listing.title}`,
        options: {
          userId: user.id,
          username: user.name,
          listingId: listing.id,
          amount: listing.price,
          metadata: { category: listing.category },
          actionUrl: `/admin/listings?search=${listing.id}`
        }
      });
    }

    // Orders
    for (let i = 0; i < 3; i++) {
      const order = getRandomItem(sampleOrders);
      const buyer = sampleUsers.find(u => u.id === order.buyerId);
      
      notifications.push({
        type: 'order_created',
        title: 'New Order Created',
        message: `${buyer?.name} placed an order for $${order.amount}`,
        options: {
          userId: order.buyerId,
          username: buyer?.name,
          orderId: order.id,
          amount: order.amount,
          metadata: { sellerId: order.sellerId },
          actionUrl: `/admin/transactions?search=${order.id}`
        }
      });
    }

    // Payment completed
    for (let i = 0; i < 3; i++) {
      const order = getRandomItem(sampleOrders);
      const buyer = sampleUsers.find(u => u.id === order.buyerId);
      
      notifications.push({
        type: 'payment_completed',
        title: 'Payment Completed',
        message: `${buyer?.name} completed payment of $${order.amount}`,
        options: {
          userId: order.buyerId,
          username: buyer?.name,
          orderId: order.id,
          amount: order.amount,
          metadata: { secretCode: '123456' },
          actionUrl: `/admin/transactions?search=${order.id}`
        }
      });
    }

    // Payment failed
    const failedOrder = getRandomItem(sampleOrders);
    const failedBuyer = sampleUsers.find(u => u.id === failedOrder.buyerId);
    notifications.push({
      type: 'payment_failed',
      title: 'Payment Failed',
      message: `Payment failed for ${failedBuyer?.name}'s order of $${failedOrder.amount}`,
      options: {
        userId: failedOrder.buyerId,
        username: failedBuyer?.name,
        orderId: failedOrder.id,
        amount: failedOrder.amount,
        metadata: { failureReason: 'Insufficient funds' },
        actionUrl: `/admin/transactions?search=${failedOrder.id}`
      }
    });

    // Feedback
    for (let i = 0; i < 2; i++) {
      const user = getRandomItem(sampleUsers);
      notifications.push({
        type: 'feedback_submitted',
        title: 'New Feedback Submitted',
        message: `${user.name} submitted feedback: Bug Report`,
        options: {
          userId: user.id,
          username: user.name,
          metadata: { type: 'bug', rating: 3 },
          actionUrl: '/admin/reports'
        }
      });
    }

    // Wallet activities
    for (let i = 0; i < 2; i++) {
      const user = getRandomItem(sampleUsers);
      const amount = getRandomAmount(20, 100);
      
      notifications.push({
        type: 'wallet_added',
        title: 'Wallet Credit Added',
        message: `${user.name} added $${amount} to wallet`,
        options: {
          userId: user.id,
          username: user.name,
          amount,
          metadata: { transactionType: 'credit' },
          actionUrl: '/admin/wallet-reports'
        }
      });
    }

    // Shipping
    const shippingOrder = getRandomItem(sampleOrders);
    const seller = sampleUsers.find(u => u.id === shippingOrder.sellerId);
    notifications.push({
      type: 'shipping_label_created',
      title: 'Shipping Label Created',
      message: `${seller?.name} created a shipping label for order`,
      options: {
        userId: shippingOrder.sellerId,
        username: seller?.name,
        orderId: shippingOrder.id,
        metadata: { carrier: 'USPS', service: 'Priority Mail' },
        actionUrl: `/admin/shipping?search=${shippingOrder.id}`
      }
    });

    // Escrow release
    const escrowOrder = getRandomItem(sampleOrders);
    const escrowSeller = sampleUsers.find(u => u.id === escrowOrder.sellerId);
    notifications.push({
      type: 'escrow_released',
      title: 'Escrow Funds Released',
      message: `Funds released to ${escrowSeller?.name} for order $${escrowOrder.amount}`,
      options: {
        userId: escrowOrder.sellerId,
        username: escrowSeller?.name,
        orderId: escrowOrder.id,
        amount: escrowOrder.amount,
        metadata: { releaseMethod: 'auto' },
        actionUrl: `/admin/transactions?search=${escrowOrder.id}`
      }
    });

    // User issue
    const issueUser = getRandomItem(sampleUsers);
    notifications.push({
      type: 'user_issue',
      title: 'New Issue Reported',
      message: `${issueUser.name} reported an issue: Payment not processing`,
      options: {
        userId: issueUser.id,
        username: issueUser.name,
        metadata: { category: 'payment', priority: 'high' },
        actionUrl: '/admin/reports'
      }
    });

    // Create all notifications
    console.log(`Creating ${notifications.length} sample notifications...`);
    
    for (const notification of notifications) {
      await AdminNotificationService.createNotification(
        notification.type,
        notification.title,
        notification.message,
        notification.options
      );
      
      // Add small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('✅ Admin notifications seeded successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding admin notifications:', error);
    throw error;
  }
};

// Function to clear all admin notifications (for testing)
export const clearAdminNotifications = async (): Promise<void> => {
  console.log('🧹 Clearing admin notifications...');
  
  try {
    // This would need to be implemented with proper Firestore batch operations
    // For now, just log the intent
    console.log('⚠️ Clear function not implemented - would clear all admin_notifications');
  } catch (error) {
    console.error('❌ Error clearing admin notifications:', error);
    throw error;
  }
};
